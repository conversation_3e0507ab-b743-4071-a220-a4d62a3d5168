import React, { useState } from 'react'
import { Form, Formik } from 'formik'
import * as Yup from 'yup'
import * as SharedStyled from '../../styles/styled'
import { CrossIcon } from '../../assets/icons/CrossIcon'
import UnitSvg from '../../assets/newIcons/unitModal.svg'
import { InputWithValidation } from '../../shared/inputWithValidation/InputWithValidation'
import { useParams } from 'react-router-dom'
import { isSuccess, notify } from '../../shared/helpers/util'
import Button from '../../shared/components/button/Button'
import { StepModalContainer } from '../newLead/lostModal/styles'
import { CrossContainer, ModalHeader, ModalHeaderContainer } from './style'
import CustomSelect from '../../shared/customSelect/CustomSelect'
import { IntendWidth } from '../contact/style'
import { addLinkedContact, markLeadAsActive, markLeadAsInvalid } from '../../logic/apis/contact'
import SearchableDropdown from '../../shared/searchableDropdown/SearchableDropdown'
import AutoComplete from '../../shared/autoComplete/AutoComplete'
import { relationshipOptions } from '../contact/components/addRelationshipContactModal/AddRelationshipContact'
import { Types } from '../contact/constant'

interface I_InvalidLeadModal {
  onClose: () => void
  onComplete: () => void
  isInvalid: boolean
  setIsInvalid: React.Dispatch<React.SetStateAction<boolean>>
  // fetchActivity: () => Promise<void>
  // initFetchContact: () => Promise<void>
  leadId: string
  fetchSearchContact: any
  fetchLinkedContact: any
}

interface I_initialValues {
  reason: string
  notes: string
  type: string
  relationship: string
}

// Define the structured data for invalid lead reasons
interface SubOption {
  label: string
  field?: string
  action?: string
}

const invalidLeadReasons = {
  'Current Client': {},
  'Looking For Work': {
    type: Types.Other,
    subOptions: [{ label: 'What position?', field: 'notes' }] as SubOption[],
  },
  'Outside Service Area': {
    type: Types.Lead,
    subOptions: [{ label: 'Where are they located?', field: 'notes' }] as SubOption[],
  },
  'Purchase material only': {
    type: Types.Lead,
    subOptions: [{ label: 'What kind of project?', field: 'notes' }] as SubOption[],
  },
  'Service Not Provided': {
    type: Types.Lead,
    subOptions: [{ label: 'What service do they need?', field: 'notes' }] as SubOption[],
  },
  Spam: {
    type: Types.Spam,
    subOptions: [{ label: 'Selling what?', field: 'notes' }] as SubOption[],
  },
  'Bad Contact Information': {
    type: Types.Lead,
    subOptions: [{ label: 'Phone/Email? How do you know it was bad?', field: 'notes' }] as SubOption[],
  },
  'Warranty Call': {
    type: '',
  },
  Vendor: {
    type: Types.Vendor,
    subOptions: [{ label: 'Vendor name?', field: 'notes' }] as SubOption[],
  },
  Other: {
    type: Types.Lead,
    subOptions: [{ label: 'Invalid lead notes', field: 'notes' }] as SubOption[],
  },
}

console.log('TEST===[log]===>', Object.keys(invalidLeadReasons))

const InvalidLeadModal: React.FC<I_InvalidLeadModal> = (props) => {
  const [selectedContact, setSelectedContact] = useState<any>({})
  const { onClose, onComplete, isInvalid, setIsInvalid, leadId, fetchSearchContact, fetchLinkedContact } = props
  const initialValues: I_initialValues = {
    reason: '',
    notes: '',
    type: '',
    relationship: '',
  }
  const invalidLeadSchema = Yup.object().shape({
    reason: !isInvalid ? Yup.string().required('Required') : Yup.string(),
    notes: !isInvalid
      ? Yup.string().when('reason', {
          is: 'Other',
          then: Yup.string().required('Required'),
          otherwise: Yup.string(),
        })
      : Yup.string().notRequired(),
    type: !isInvalid ? Yup.string().required('Required') : Yup.string(),
    relationship: Yup.string().when('reason', {
      is: 'Current Client',
      then: Yup.string().required('Required'),
      otherwise: Yup.string(),
    }),
  })

  const { contactId } = useParams()
  const [loading, setLoading] = useState(false)

  const handleSubmit = async (values: typeof initialValues) => {
    setLoading(true)
    try {
      if (values?.reason === 'Current Client') {
        const addLinkedObj = [
          {
            relationship: values.relationship,
            id: selectedContact._id,
          },
        ]
        const res = await addLinkedContact(addLinkedObj, contactId!)

        if (isSuccess(res)) {
          notify('Linked Contact Added Successfully', 'success')
          fetchLinkedContact()
        }
      }

      // Create the new payload structure
      const payload = {
        reason: values.reason,
        notes: values.notes,
        type: values.type,
      }

      const response = await (!isInvalid ? markLeadAsInvalid(payload, leadId) : markLeadAsActive(leadId))

      if (isSuccess(response)) {
        setIsInvalid((prev: boolean) => !prev)
        // fetchActivity()
        notify(`Lead marked as ${isInvalid ? 'valid' : 'invalid'}!`, 'success')
        setLoading(false)
        onComplete()
      } else throw new Error(response?.data?.message)
    } catch (err) {
      console.log(err)
    } finally {
      onClose()
      setLoading(false)
    }
  }

  return (
    <StepModalContainer>
      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        onSubmit={handleSubmit}
        validationSchema={invalidLeadSchema}
        validateOnChange={true}
        validateOnBlur={false}
      >
        {({ values, errors, touched, resetForm, setFieldValue }) => {
          return (
            <>
              <ModalHeaderContainer>
                <SharedStyled.FlexRow>
                  <img src={UnitSvg} alt="modal icon" />
                  <SharedStyled.FlexCol>
                    <ModalHeader>{isInvalid ? 'Mark as Valid' : 'Mark as Invalid'} Lead</ModalHeader>
                  </SharedStyled.FlexCol>
                </SharedStyled.FlexRow>

                <CrossContainer
                  onClick={() => {
                    resetForm()
                    onClose()
                  }}
                >
                  <CrossIcon />
                </CrossContainer>
              </ModalHeaderContainer>
              <SharedStyled.SettingModalContentContainer>
                <Form className="form">
                  <SharedStyled.Content maxWidth="706px" width="100%" disableBoxShadow={true} noPadding={true}>
                    {!isInvalid && (
                      <>
                        <CustomSelect
                          labelName="Invalid Lead Reason*"
                          stateName="reason"
                          value={values?.reason || ''}
                          error={!!(touched?.reason && errors?.reason)}
                          setFieldValue={(field: string, value: string) => {
                            setFieldValue(field, value)
                            // Auto-populate type based on reason selection
                            if (value && invalidLeadReasons[value as keyof typeof invalidLeadReasons]) {
                              setFieldValue('type', invalidLeadReasons[value as keyof typeof invalidLeadReasons].type)
                            }
                          }}
                          setValue={() => {}}
                          dropDownData={Object?.keys(invalidLeadReasons)}
                          innerHeight="52px"
                          margin="10px 0 0 0"
                        />

                        {/* Show type field */}
                        {values?.reason && (
                          <IntendWidth>
                            <InputWithValidation
                              labelName="Type*"
                              stateName="type"
                              value={values.type}
                              disabled={true}
                              error={touched.type && errors.type ? true : false}
                            />
                          </IntendWidth>
                        )}

                        {/* Show notes field for applicable reasons */}
                        {values?.reason &&
                          invalidLeadReasons[values.reason as keyof typeof invalidLeadReasons]?.subOptions.some(
                            (option) => option.field === 'notes'
                          ) && (
                            <IntendWidth>
                              <InputWithValidation
                                labelName={
                                  invalidLeadReasons[values.reason as keyof typeof invalidLeadReasons]?.subOptions.find(
                                    (option) => option.field === 'notes'
                                  )?.label || 'Notes*'
                                }
                                stateName="notes"
                                error={touched.notes && errors.notes ? true : false}
                              />
                            </IntendWidth>
                          )}
                        {values?.reason === 'Current Client' && (
                          <div
                            style={{
                              width: '100%',
                            }}
                          >
                            <div
                              style={{
                                margin: '10px 0',
                              }}
                            >
                              <SharedStyled.Text familyTypeMedium>Who is this contact linked to?</SharedStyled.Text>
                            </div>

                            <IntendWidth width="95%">
                              <SharedStyled.FlexBox width="100%" flexDirection="column" gap="12px">
                                <SearchableDropdown
                                  className="relative"
                                  label="Search Contacts"
                                  placeholder="Type to search"
                                  searchFunction={fetchSearchContact}
                                  selectedValue={selectedContact?.fullName}
                                  displayKey={'fullName'}
                                  onSelect={(item: any) => {
                                    setSelectedContact(item)
                                  }}
                                  hideDropdown
                                  resultExtractor={(res) => res?.data?.data?.contacts || []}
                                />

                                <SharedStyled.FlexBox flexDirection="column">
                                  {selectedContact?.fullName ? (
                                    <SharedStyled.Text familyTypeMedium>
                                      {selectedContact?.fullName} is their:
                                    </SharedStyled.Text>
                                  ) : (
                                    <div />
                                  )}
                                  <AutoComplete
                                    labelName="Relationship*"
                                    className="relative"
                                    dropdownHeight="300px"
                                    stateName="relationship"
                                    value={values.relationship}
                                    options={relationshipOptions}
                                    error={touched.relationship && errors.relationship ? true : false}
                                    setFieldValue={setFieldValue}
                                    setValueOnClick={(val: string) => {
                                      setFieldValue('relationship', val)
                                    }}
                                  />
                                </SharedStyled.FlexBox>
                              </SharedStyled.FlexBox>
                            </IntendWidth>
                          </div>
                        )}
                      </>
                    )}
                    <SharedStyled.ButtonContainer marginTop="20px">
                      <Button type="submit" maxWidth="150px" isLoading={loading}>
                        Submit
                      </Button>
                    </SharedStyled.ButtonContainer>
                  </SharedStyled.Content>
                </Form>
              </SharedStyled.SettingModalContentContainer>
            </>
          )
        }}
      </Formik>
    </StepModalContainer>
  )
}

export default InvalidLeadModal
