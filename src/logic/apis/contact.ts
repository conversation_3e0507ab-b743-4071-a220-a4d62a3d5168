import { AxiosInstance } from '.'
import { fields } from '../../modules/formBuilder/constant'
import { filterUndefinedAndNull, notify, simplifyBackendError } from '../../shared/helpers/util'

interface I_CreateContact {
  firstName: string
  lastName: string
  street: string
  city: string
  state: string
  zip: string
  phone: string
  email: string
  leadSource: string
  workType?: string
  newLeadDate?: string
  csrId?: string
  referredBy?: string
  leadSourceId?: string
  campaignId?: string
  type: string
  leadSourceName: string
  linkedContacts?: {
    id?: string
    relationship?: string
  }[]
  notes?: string
  createdBy: string
  isBusiness: boolean
  lat: string
  long: string
}

interface I_DeleteContact {
  id: string
}
interface I_PermanentDeleteContact {
  id: string
}
interface I_RestoreContact {
  id: string
}
interface I_UpdateContact {
  contactId?: string
  businessName?: string
  street: string
  city: string
  state: string
  zip: string
  phone: string
  email: string
  leadSource: string
  leadSourceName: string
  notes?: string
  csrId?: string
  referredBy?: string
  leadSourceId?: string
  campaignId?: string
  type: string
  linkedContacts?: {
    id?: string
    relationship?: string
  }[]
  workType?: string
  newLeadDate?: string
  firstName?: string
  lastName?: string
  fullName?: string
  isBusiness?: boolean
  // duration?:number
  createdBy: string
}

interface I_GetContacts {
  deleted: boolean
  skip?: number
  limit?: number
  search?: string
  filter?: any
}

interface I_UpdateContactLead {
  leadSourceId?: string | null
  campaignId?: string | null
  referredBy?: string
  workType?: string
  trackingRuleId?: string | null
  newLeadDate?: string
}
// Action and Comment interfaces
interface I_CreateContactAction {
  memberId: string
  type: string
  body: string
  assignTo?: string
  dueDate: any
  currDate: Date
  id?: string
}

interface I_CompleteContactAction {
  id: string
  memberId: string
  assignTo?: string
  type: string
  body: string
  dueDate: any
  currDate: Date
}

interface I_CreateContactComment {
  memberId: string
  body: string
  currDate: string
  id?: string
}

interface I_UpdateContactComment {
  id: string
  memberId: string
  body: string
  currDate: string
}

interface I_UpdateActivity {
  id: string
  body: string
  currDate: string
  memberId: string
}

interface I_LostLead {
  reason: string
  date: string
  memberId: string
}

// TODO: Remove business name
export const createContact = async (data: I_CreateContact) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.post(`/contact`, filterUndefinedAndNull(data, true, true), {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('createContact error', error)
    return error?.response
  }
}

export const deleteContact = async (id: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.delete(`/contact/id/${id}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('deleteContact error', error)
    return error?.response
  }
}

export const deleteMultipleContacts = async (data: any) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.delete(`/contact/multiple`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data,
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('deleteMultipleContacts error', error)
    return error?.response
  }
}

export const getLeads = async (status: string, deleted: boolean) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/contact/lead/status/${status}/deleted/${deleted}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('getLeads error', error)
    return error?.response
  }
}

export const permanentDeleteContact = async (id: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.delete(`/contact/permanently-delete/${id}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('permanentDeleteContact error', error)
    return error?.response
  }
}

export const getContactOpportunities = async (contactId: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/contact/opportunities/${contactId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('getContactOpportunities error', error)
    return error?.response
  }
}

export const restoreContact = async (id: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.put(`/contact/restore/${id}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('restoreContact error', error)
    return error?.response
  }
}

export const updateContact = async (dataObj: I_UpdateContact, id: string) => {
  try {
    const data = filterUndefinedAndNull(dataObj, false, false)
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.put(`/contact/id/${id}`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('updateContact error', error)
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error?.response
  }
}

export const migrateContact = async (toContactId: string, mergedFields: any) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.put(`/contact/migrate-opp/toContact/${toContactId}`, mergedFields, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('migrateContact error', error)
    return error?.response
  }
}

export const getImportedContacts = async ({ limit, search, skip, deleted, filter }: I_GetContacts) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/contact/imported`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        skip,
        limit,
        deleted,
        search,
        filter: JSON.stringify(filter.filter),
      },
    })

    return response
  } catch (error: any) {
    console.error('getImportedContacts error', error)
    return error?.response
  }
}

export const getContacts = async ({ limit, search, skip, deleted, filter }: I_GetContacts) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/contact`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        skip,
        limit,
        deleted,
        search,
        filter: JSON.stringify(filter.filter),
      },
      // paramsSerializer: (params) => {
      //   return JSON.stringify(params)
      // },
    })

    return response
  } catch (error: any) {
    console.log({ error })
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
  }
}

export const getContactById = async (contactId: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/contact/id/${contactId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
  }
}

export const getSearchedContact = async (search: string, dataObj?: any) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/contact/search`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        search,
        fields: JSON?.stringify(dataObj?.fields) || undefined,
        type: dataObj?.type,
      },
    })

    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
  }
}

export const addLinkedContact = async (data: { relationship: string; id: string }[], id: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.put(`/contact/add-linked-contact/${id}`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
  }
}

export const removeLinkedContact = async (id: string, linkedContactId: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.delete(`/contact/remove-linked-contact/${id}/${linkedContactId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
  }
}

export const getLinkedContact = async (id: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/contact/get-linked-contact/${id}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
  }
}

// Update a activity for a contact
export const updateActivity = async (data: I_UpdateActivity, id: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.put(`/contact/activity/${id}`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('updateActivity error', error)
    return error?.response
  }
}

// Create a new action for a contact
export const createContactAction = async (data: I_CreateContactAction, contactId: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.post(`/contact/action/${contactId}`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('createContactAction error', error)
    return error?.response
  }
}

// Complete/update an action for a contact
export const completeContactAction = async (data: I_CompleteContactAction, contactId: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.put(`/contact/action/complete/${contactId}`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('completeContactAction error', error)
    return error?.response
  }
}

// Get activity for a contact
export const getContactActivity = async (contactId: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/contact/activity/${contactId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('getContactActivity error', error)
    return error?.response
  }
}

// Create a comment for a contact
export const createContactComment = async (data: I_CreateContactComment, contactId: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.post(`/contact/comment/${contactId}`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('createContactComment error', error)
    return error?.response
  }
}

// Update a comment for a contact
export const updateContactComment = async (data: I_UpdateContactComment, contactId: string, commentId: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.put(`/contact/comment/${contactId}/${commentId}`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('updateContactComment error', error)
    return error?.response
  }
}

// Delete a comment for a contact
export const deleteContactComment = async (contactId: string, commentId: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.delete(`/contact/comment/${contactId}/${commentId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('deleteContactComment error', error)
    return error?.response
  }
}

export const getContactLeads = async (contactId: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/contact/leads/${contactId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('getContactLeads error', error)
    return error?.response
  }
}

export const updateLead = async (data: I_UpdateContactLead, id: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/lead/id/${id}`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('updateLead error', error)
    return error?.response
  }
}

export const markLeadAsActive = async (id: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(
      `/lead/active/${id}`,
      {},
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
  }
}

export const markLeadAsInvalid = async (data: { reason: string; notes?: string; type?: string }, id: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/lead/invalid/${id}`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
  }
}

export const lostContactLead = async (data: I_LostLead, id: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/lead/lost/${id}`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
  }
}

export const unlostContactLead = async (data: I_LostLead, id: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/lead/un-lost/${id}`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
  }
}

export const updateLeadStage = async (stageId: string, id: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`lead/${id}/stage/${stageId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('updateLeadStage error', error)
    return error?.response
  }
}

// =============== Actions ===============

interface IAction {
  contactOppId: string
  isContact: boolean
  data?: I_CreateContactAction
}

export const getActionsForContactOpp = async ({ contactOppId, isContact }: IAction) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`actions/actionsWithHistory/${isContact}/${contactOppId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error) {
    console.log({ error })
  }
}

export const createActionForContactOpp = async ({ contactOppId, isContact, data }: IAction) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.post(`actions/contactOrOpp/${isContact}/${contactOppId}`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')

    console.log({ error })
  }
}

export const updateActionForContactOpp = async ({ contactOppId, isContact, data }: IAction) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.put(`actions/complete/${isContact}/${contactOppId}`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.log({ error })
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
  }
}

export const getContactReferrals = async (contactId: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/contact/referrals/${contactId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.log({ error })
  }
}

export const getContactOpportunityById = async (id: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`contact/open-opportunities-leads/${id}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('getContactOpportunityById error', error)
    return error?.response
  }
}
